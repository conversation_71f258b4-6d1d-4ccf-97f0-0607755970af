[project]
name = "sanuki-qq"
version = "0.1.0"
description = "sanuki-qq"
readme = "README.md"
requires-python = "==3.11.*"
authors = [
    {name = "Genisys", email = "<EMAIL>"},
]
license = {text = "GPL-3.0"}
dependencies = [
    "nonebot-adapter-onebot>=1.4.4",
    "nonebot2[fastapi]>=2.3.2",
    "pydantic<2.0",
    "httpx>=0.27.0",
    "openai>=1.58.1",
    "requests>=2.32.3",
    "pyexecjs>=1.5.1",
    "ddddocr>=1.5.5",
    "cachetools>=5.4.0",
    "nonebot-plugin-htmlrender>=0.3.5",
    "nonebot-adapter-qq>=1.5.2",
    "aiohttp>=3.10.10",
    "zhdate>=0.1",
    "beautifulsoup4>=4.13.4",
    "zhconv>=1.4.3",
]

[tool.nonebot]
adapters = [
    { name = "onebot", module_name = "nonebot.adapters.onebot.v11" },
]
plugins = []
plugin_dirs = [
    "src/plugins",
]
builtin_plugins = []

[tool.pdm]
distribution = false

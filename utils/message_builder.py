import base64
import io

from PIL import Image
from nonebot.adapters.onebot.v11 import MessageSegment


def pic2b64(pic: Image) -> str:
    buf = io.BytesIO()
    pic.save(buf, format="PNG")
    base64_str = base64.b64encode(buf.getvalue()).decode()
    return "base64://" + base64_str


def image(
        b64: str = None,
) -> MessageSegment:
    file = b64 if b64.startswith("base64://") else ("base64://" + b64)
    if file.startswith(("http", "base64://")):
        return MessageSegment.image(file)
